package main

import (
	"flag"
	"log"
	"net/http"
	"socks/server/server/handlers"
	util "socks/server/util"
	"strconv"
	"time"
)

var (
	// itDao = dao.GetIntranetTunnelDaoImpl()
	// 获取系统配置
	sysConfig           *util.TunnelConfig
	portCacheExpiration = 30 * time.Minute
)

func main() {
	// 设置详细日志
	log.SetFlags(log.Ldate | log.Ltime | log.Lmicroseconds | log.Lshortfile)

	// 使用配置的管理端口，但允许通过命令行参数覆盖
	configPath := flag.String("config", "", "配置文件")
	flag.Parse()
	sysConfig = util.GetSystemConfig(*configPath)

	// 使用配置的过期时间（天转换为分钟）
	defaultCacheExpiration := sysConfig.SlidingExpiration * 24 * 60 // 天转换为分钟
	cacheExpiration := flag.Int("cache", defaultCacheExpiration, "端口缓存过期时间(分钟)")

	// 设置缓存过期时间
	if *cacheExpiration > 0 {
		portCacheExpiration = time.Duration(*cacheExpiration) * time.Minute
	}

	// Start TCP proxy server
	tcpProxyHandler := handlers.GetTCPProxyHandler()
	if err := tcpProxyHandler.StartTCPProxyServer(sysConfig.URLProxyPort); err != nil {
		log.Fatalf("start TCP proxy server fail: %v", err)
	}

	// Start HTTP server for allocation API
	log.Printf("HTTP API server listening on port %d", sysConfig.ManagerPort)
	log.Printf("TCP Proxy server listening on port %d", sysConfig.URLProxyPort)
	log.Printf("Server configuration: Port range %d-%d, Max connections %d, Timeout %ds",
		sysConfig.MinPort, sysConfig.MaxPort, sysConfig.MaxConnection, sysConfig.Timeout)
	log.Printf("Port cache expiration: %v", portCacheExpiration)

	// 新增的
	http.HandleFunc("/register", handlers.GetPortProxyHandler().RegisterHandler)
	http.HandleFunc("/allocate", handlers.GetPortProxyHandler().AllocateHandler)
	http.HandleFunc("/tunnel/refresh", handlers.GetPortProxyHandler().RefreshHandler)

	http.HandleFunc("/url/register", handlers.GetURLProxyHandler().RegisterURLHandler)
	http.HandleFunc("/url/unregister", handlers.GetURLProxyHandler().UnregisterURLHandler)

	// 通用代理处理器，处理所有其他路径
	http.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		// 首先尝试URL代理
		if r.URL.Path != "/" {
			handlers.GetURLProxyHandler().ProxyHandler(w, r)
			return
		}
		// 如果是根路径，返回404
		http.NotFound(w, r)
	})

	http.ListenAndServe("0.0.0.0:"+strconv.Itoa(sysConfig.ManagerPort), RequestLogger(http.DefaultServeMux))
}

func RequestLogger(targetMux http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 请求一进入就记录
		log.Printf("%s %s %s from %s", time.Now().Format("2006-01-02 15:04:05"), r.Method, r.RequestURI, r.RemoteAddr)

		// 执行实际处理
		targetMux.ServeHTTP(w, r)
	})
}
