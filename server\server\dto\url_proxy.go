package dto

type URLRegisterRequest struct {
	ApiType      string `json:"api_type"`      // Agent, AI, or API
	ServiceGroup string `json:"service_group"` // 服务组别
	AppName      string `json:"app_name"`      // 应用名称
	ClientName   string `json:"client_name"`   // 客户端名称
	ClientUUID   string `json:"client_id"`     // 客户端UUID
	BaseURL      string `json:"base_url"`      // 基础URL，客户端服务的基础路径
}

type URLRegisterResponse struct {
	Success bool   `json:"success"`
	URLPath string `json:"url_path"` // 代理路径，用于客户端访问服务端的路径
}

type URLUnregisterRequest struct {
}

type URLUnregisterResponse struct {
}
